<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Test - Thumbnail Generator</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        .thumbnail-preview {
            max-width: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px 0;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Debug Test - Thumbnail Generator</h1>

    <div class="test-section">
        <h2>1. Test Fallback Thumbnail Generation</h2>
        <p>This tests the fallback SVG thumbnail generation when APIs fail.</p>
        <input type="text" id="fallback-title" placeholder="Enter video title..." style="width: 300px; padding: 8px;">
        <button onclick="testFallbackGeneration()">Generate Fallback Thumbnail</button>
        <div id="fallback-result"></div>
    </div>

    <div class="test-section">
        <h2>2. Test Storage Functions</h2>
        <p>Test local storage save/load functionality.</p>
        <button onclick="testStorage()">Test Storage</button>
        <div id="storage-result"></div>
    </div>

    <div class="test-section">
        <h2>3. Test Full Generation Pipeline</h2>
        <p>Test the complete thumbnail generation process with fallbacks.</p>
        <input type="text" id="full-title" placeholder="Enter video title..." style="width: 300px; padding: 8px;">
        <button onclick="testFullGeneration()">Test Full Generation</button>
        <div id="full-result"></div>
    </div>

    <div class="test-section">
        <h2>4. Debug Log</h2>
        <button onclick="clearLog()">Clear Log</button>
        <div id="debug-log" class="log"></div>
    </div>

    <!-- Include services -->
    <script src="gemini-pro-service.js"></script>
    <script src="stability-pro-service.js"></script>

    <script>
        // Initialize services
        const replicatePro = new ReplicateProService();
        const debugLog = document.getElementById('debug-log');

        // Override console for logging
        const originalLog = console.log;
        const originalError = console.error;

        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            if (type === 'error') logEntry.style.color = '#dc3545';
            if (type === 'success') logEntry.style.color = '#28a745';
            debugLog.appendChild(logEntry);
            debugLog.scrollTop = debugLog.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };

        function showResult(containerId, message, isSuccess, thumbnail = null) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);

            if (thumbnail) {
                const img = document.createElement('img');
                img.src = thumbnail;
                img.className = 'thumbnail-preview';
                img.alt = 'Generated Thumbnail';
                container.appendChild(img);
            }
        }

        async function testFallbackGeneration() {
            const title = document.getElementById('fallback-title').value.trim();
            if (!title) {
                alert('Please enter a video title');
                return;
            }

            document.getElementById('fallback-result').innerHTML = '';
            console.log(`🧪 Testing fallback generation for: "${title}"`);

            try {
                const prompt = `Professional YouTube thumbnail for "${title}", vibrant colors, bold typography`;
                const fallbackThumbnail = await replicatePro.generateFallbackThumbnail(prompt);

                showResult('fallback-result', '✅ Fallback thumbnail generated successfully!', true, fallbackThumbnail);
                console.log('Fallback generation successful');
            } catch (error) {
                showResult('fallback-result', `❌ Fallback generation failed: ${error.message}`, false);
                console.error('Fallback generation failed:', error);
            }
        }

        function testStorage() {
            document.getElementById('storage-result').innerHTML = '';
            console.log('🧪 Testing storage functions...');

            try {
                // Test save
                const testData = {
                    test: 'data',
                    timestamp: new Date().toISOString(),
                    number: 42
                };

                localStorage.setItem('debug_test', JSON.stringify(testData));
                console.log('Data saved to localStorage');

                // Test load
                const loadedData = JSON.parse(localStorage.getItem('debug_test'));
                console.log('Data loaded from localStorage:', loadedData);

                if (JSON.stringify(testData) === JSON.stringify(loadedData)) {
                    showResult('storage-result', '✅ Storage test passed!', true);
                } else {
                    showResult('storage-result', '❌ Storage test failed - data mismatch', false);
                }

                // Cleanup
                localStorage.removeItem('debug_test');
            } catch (error) {
                showResult('storage-result', `❌ Storage test failed: ${error.message}`, false);
                console.error('Storage test failed:', error);
            }
        }

        async function testFullGeneration() {
            const title = document.getElementById('full-title').value.trim();
            if (!title) {
                alert('Please enter a video title');
                return;
            }

            document.getElementById('full-result').innerHTML = '';
            console.log(`🧪 Testing full generation pipeline for: "${title}"`);

            try {
                const prompt = `Professional YouTube thumbnail for "${title}", vibrant colors, bold typography, high contrast`;

                showResult('full-result', '🔄 Starting generation...', true);

                const result = await replicatePro.generateWithProgress([prompt], (progress) => {
                    console.log(`Progress: ${progress.percentage}% - ${progress.status}`);
                    showResult('full-result', `🔄 ${progress.status} (${progress.percentage}%)`, true);
                });

                if (result.success && result.thumbnails.length > 0) {
                    const message = result.isFallback ?
                        '✅ Fallback thumbnail generated (API unavailable)' :
                        '✅ AI thumbnail generated successfully!';
                    showResult('full-result', message, true, result.thumbnails[0]);
                } else {
                    showResult('full-result', `❌ Generation failed: ${result.failed?.[0]?.error || 'Unknown error'}`, false);
                }
            } catch (error) {
                showResult('full-result', `❌ Generation error: ${error.message}`, false);
                console.error('Full generation test failed:', error);
            }
        }

        function clearLog() {
            debugLog.innerHTML = '';
        }

        // Auto-run basic tests on load
        window.addEventListener('load', () => {
            console.log('🚀 Debug Test Page Loaded');
            console.log('Ready for testing thumbnail generation pipeline');
            testStorage(); // Auto-test storage
        });
    </script>
</body>
</html>
