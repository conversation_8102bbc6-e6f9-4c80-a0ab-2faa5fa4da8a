<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Debug Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            background: #fafbfc;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #3182ce;
            background: #f7fafc;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #3182ce;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>File Upload Debug Test</h1>
    
    <div class="test-section">
        <h3>1. Element Detection Test</h3>
        <div id="element-status"></div>
        <button onclick="testElements()">Test Elements</button>
    </div>

    <div class="test-section">
        <h3>2. Click Handler Test</h3>
        <div class="upload-area" id="test-upload-area">
            <div>📁 Click here to test file input trigger</div>
        </div>
        <input type="file" id="test-file-input" multiple accept="image/*" style="display: none;">
        <div id="click-status"></div>
    </div>

    <div class="test-section">
        <h3>3. File Selection Test</h3>
        <div id="file-status"></div>
        <button onclick="clearFiles()">Clear Files</button>
    </div>

    <div class="test-section">
        <h3>4. Console Log</h3>
        <div class="log" id="console-log"></div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        // Override console.log to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const logElement = document.getElementById('console-log');

        function addToLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : '✅';
            logElement.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };

        // Test elements
        function testElements() {
            const statusDiv = document.getElementById('element-status');
            let html = '';

            // Test main app elements
            const uploadArea = document.getElementById('upload-area');
            const fileInput = document.getElementById('file-input');
            const uploadBtn = document.getElementById('upload-btn');

            html += `<div class="${uploadArea ? 'success' : 'error'}">Upload Area: ${uploadArea ? '✅ Found' : '❌ Not Found'}</div>`;
            html += `<div class="${fileInput ? 'success' : 'error'}">File Input: ${fileInput ? '✅ Found' : '❌ Not Found'}</div>`;
            html += `<div class="${uploadBtn ? 'success' : 'error'}">Upload Button: ${uploadBtn ? '✅ Found' : '❌ Not Found'}</div>`;

            // Test if app is initialized
            const appExists = typeof window.app !== 'undefined';
            html += `<div class="${appExists ? 'success' : 'error'}">App Instance: ${appExists ? '✅ Found' : '❌ Not Found'}</div>`;

            statusDiv.innerHTML = html;
            console.log('Element test completed');
        }

        // Test click handler
        const testUploadArea = document.getElementById('test-upload-area');
        const testFileInput = document.getElementById('test-file-input');
        const clickStatus = document.getElementById('click-status');

        testUploadArea.addEventListener('click', () => {
            console.log('Test upload area clicked');
            clickStatus.innerHTML = '<div class="info">Upload area clicked - triggering file input...</div>';
            testFileInput.click();
        });

        testFileInput.addEventListener('change', (e) => {
            const files = e.target.files;
            console.log('Test file input changed, files:', files.length);
            
            const fileStatus = document.getElementById('file-status');
            let html = `<div class="success">✅ File selection works! Selected ${files.length} files:</div>`;
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                html += `<div class="info">📄 ${file.name} (${file.type}, ${(file.size/1024).toFixed(1)}KB)</div>`;
            }
            
            fileStatus.innerHTML = html;
        });

        function clearFiles() {
            testFileInput.value = '';
            document.getElementById('file-status').innerHTML = '';
            console.log('Files cleared');
        }

        function clearLog() {
            logElement.textContent = '';
        }

        // Auto-run element test on load
        window.addEventListener('load', () => {
            console.log('Debug page loaded');
            setTimeout(testElements, 100);
        });

        // Test if main app exists
        setTimeout(() => {
            if (typeof window.app !== 'undefined') {
                console.log('Main app detected:', window.app);
            } else {
                console.error('Main app not found - check for JavaScript errors');
            }
        }, 500);
    </script>
</body>
</html>
