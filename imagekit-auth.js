// ImageKit Authentication Helper
// This file provides client-side ImageKit integration with proper authentication

class ImageKitManager {
    constructor() {
        this.imageKit = null;
        this.credentials = null;
    }

    initialize(credentials) {
        this.credentials = credentials;
        
        // Initialize ImageKit with client-side configuration
        this.imageKit = new ImageKit({
            urlEndpoint: credentials.urlEndpoint,
            publicKey: credentials.publicKey,
            // Note: Private key should never be exposed on client-side
            // In production, you need a backend endpoint for authentication
        });

        return this.imageKit;
    }

    // Simulate authentication endpoint (in production, this should be a backend service)
    async getAuthenticationParameters() {
        try {
            // In a real implementation, this would call your backend
            // which would use the private key to generate authentication parameters
            
            // For demo purposes, we'll return mock parameters
            // DO NOT use private keys on the client side in production!
            const token = this.generateToken();
            const expire = Math.floor(Date.now() / 1000) + 2400; // 40 minutes from now
            const signature = this.generateSignature(token, expire);

            return {
                token: token,
                expire: expire,
                signature: signature
            };
        } catch (error) {
            console.error('Error getting authentication parameters:', error);
            throw error;
        }
    }

    generateToken() {
        // Generate a random token
        return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    }

    generateSignature(token, expire) {
        // In production, this signature should be generated on your backend
        // using the private key and HMAC-SHA1 algorithm
        // This is just a mock implementation
        return btoa(`${token}${expire}`).substring(0, 20);
    }

    async uploadFile(file, options = {}) {
        try {
            if (!this.imageKit) {
                throw new Error('ImageKit not initialized');
            }

            // Get authentication parameters
            const authParams = await this.getAuthenticationParameters();

            // Prepare upload parameters
            const uploadParams = {
                file: file,
                fileName: options.fileName || `thumbnail_${Date.now()}`,
                folder: options.folder || '/user_thumbnails',
                useUniqueFileName: true,
                tags: options.tags || ['thumbnail', 'user_upload'],
                ...authParams
            };

            // Upload file
            const result = await this.imageKit.upload(uploadParams);
            
            return {
                success: true,
                data: {
                    fileId: result.fileId,
                    name: result.name,
                    url: result.url,
                    thumbnailUrl: result.thumbnailUrl,
                    size: result.size,
                    filePath: result.filePath,
                    uploadedAt: new Date().toISOString()
                }
            };

        } catch (error) {
            console.error('ImageKit upload failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async uploadMultipleFiles(files, options = {}) {
        const uploadPromises = files.map((file, index) => {
            const fileOptions = {
                ...options,
                fileName: options.fileName ? `${options.fileName}_${index}` : `thumbnail_${Date.now()}_${index}`
            };
            return this.uploadFile(file, fileOptions);
        });

        try {
            const results = await Promise.all(uploadPromises);
            const successful = results.filter(result => result.success);
            const failed = results.filter(result => !result.success);

            return {
                successful: successful.map(result => result.data),
                failed: failed.map(result => result.error),
                totalUploaded: successful.length,
                totalFailed: failed.length
            };
        } catch (error) {
            console.error('Batch upload failed:', error);
            throw error;
        }
    }

    async listFiles(options = {}) {
        try {
            if (!this.imageKit) {
                throw new Error('ImageKit not initialized');
            }

            const listParams = {
                path: options.path || '/user_thumbnails',
                limit: options.limit || 50,
                skip: options.skip || 0
            };

            const result = await this.imageKit.listFiles(listParams);
            return result;
        } catch (error) {
            console.error('Error listing files:', error);
            throw error;
        }
    }

    async deleteFile(fileId) {
        try {
            if (!this.imageKit) {
                throw new Error('ImageKit not initialized');
            }

            const result = await this.imageKit.deleteFile(fileId);
            return result;
        } catch (error) {
            console.error('Error deleting file:', error);
            throw error;
        }
    }

    // Transform image URL with ImageKit transformations
    transformImage(url, transformations) {
        if (!this.imageKit) {
            return url;
        }

        try {
            return this.imageKit.url({
                src: url,
                transformation: transformations
            });
        } catch (error) {
            console.error('Error transforming image:', error);
            return url;
        }
    }

    // Get optimized thumbnail URL
    getThumbnailUrl(originalUrl, options = {}) {
        const defaultTransformations = [
            {
                width: options.width || 300,
                height: options.height || 169, // 16:9 aspect ratio
                crop: 'maintain_ratio'
            },
            {
                quality: options.quality || 80,
                format: options.format || 'webp'
            }
        ];

        return this.transformImage(originalUrl, defaultTransformations);
    }

    // Validate ImageKit credentials
    async validateCredentials(credentials) {
        try {
            const testImageKit = new ImageKit({
                urlEndpoint: credentials.urlEndpoint,
                publicKey: credentials.publicKey
            });

            // Try to list files to validate credentials
            await testImageKit.listFiles({ limit: 1 });
            return { valid: true };
        } catch (error) {
            return { 
                valid: false, 
                error: error.message 
            };
        }
    }

    // Get storage usage statistics
    async getStorageStats() {
        try {
            if (!this.imageKit) {
                throw new Error('ImageKit not initialized');
            }

            // This would typically call ImageKit's usage API
            // For now, return mock data
            return {
                totalFiles: 0,
                totalSize: 0,
                thumbnailCount: 0,
                lastUpload: null
            };
        } catch (error) {
            console.error('Error getting storage stats:', error);
            return null;
        }
    }
}

// Export for use in main application
window.ImageKitManager = ImageKitManager;
