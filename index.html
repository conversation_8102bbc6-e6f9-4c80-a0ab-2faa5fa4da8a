 <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Free Thumbnail Generator</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Enhanced AI Services -->
    <script src="gemini-pro-service.js"></script>
    <script src="stability-pro-service.js"></script>
</head>
<body>
    <div class="container">
        <!-- Step 1: Upload Previous Thumbnails -->
        <div class="card" id="upload-card">
            <div class="card-header">
                <span class="brand">FREE THUMBNAIL GENERATOR</span>
            </div>
            <h2>Upload Previous Thumbnails</h2>
            <p class="description">Save your thumbnail images and the AI will learn your style.</p>

            <div class="upload-area" id="upload-area">
                <div class="upload-icon">📁</div>
                <div class="upload-text">
                    Drop your files here<br>
                    or click to browse
                </div>
                <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
            </div>

            <button class="btn-primary" id="upload-btn">Upload</button>

            <div class="file-preview" id="file-preview" style="display: none;">
                <div class="preview-grid" id="preview-grid"></div>
            </div>
        </div>

        <!-- Step 2: Personalize Your Thumbnail Style -->
        <div class="card" id="generate-card" style="display: none;">
            <div class="card-header">
                <span class="brand">FREE THUMBNAIL GENERATOR</span>
            </div>
            <h2>Personalize Your Thumbnail Style</h2>
            <p class="description">The AI has been trained on your thumbnails. Enter a new video title to create a style-consistent thumbnail.</p>

            <div class="input-group">
                <label for="video-title">Video Title</label>
                <input type="text" id="video-title" placeholder="Enter your video title here...">
            </div>

            <button class="btn-primary" id="generate-btn">Generate Thumbnail</button>
        </div>

        <!-- Step 3: Training AI Model -->
        <div class="card" id="training-card" style="display: none;">
            <div class="card-header">
                <span class="brand">FREE THUMBNAIL GENERATOR</span>
            </div>
            <h2>Training AI Model...</h2>
            <p class="description">Your thumbnail style is being learned by the AI. This may take a few min.</p>

            <div class="progress-container">
                <div class="progress-bar">
                    <div class="progress-fill" id="progress-fill"></div>
                </div>
                <div class="progress-text" id="progress-text">0%</div>
            </div>

            <button class="btn-primary" id="continue-btn" style="display: none;">Continue</button>
        </div>

        <!-- Step 4: Generated Thumbnail -->
        <div class="card" id="result-card" style="display: none;">
            <div class="card-header">
                <span class="brand">FREE THUMBNAIL GENERATOR</span>
            </div>
            <h2>🎉 Your Personalized Thumbnail</h2>

            <div class="result-container">
                <div class="thumbnail-result">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjNEE5MEU2Ii8+Cjx0ZXh0IHg9IjE2MCIgeT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyNCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IndoaXRlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5IT1cgVE88L3RleHQ+Cjx0ZXh0IHg9IjE2MCIgeT0iOTAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIyOCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IiNGRkQ3MDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiPlNDT1JFPC90ZXh0Pgo8dGV4dCB4PSIxNjAiIHk9IjEyMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI4IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkEgR09BTDwvdGV4dD4KPHN2ZyB4PSIyNDAiIHk9IjEwMCIgd2lkdGg9IjYwIiBoZWlnaHQ9IjYwIiB2aWV3Qm94PSIwIDAgMjQgMjQiIGZpbGw9Im5vbmUiPgo8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgZmlsbD0iI0ZGRjciLz4KPHN2ZyB4PSI4IiB5PSI4IiB3aWR0aD0iOCIgaGVpZ2h0PSI4IiB2aWV3Qm94PSIwIDAgMjQgMjQiIGZpbGw9Im5vbmUiPgo8cGF0aCBkPSJNMTIgMkw2IDIySDEyTDE4IDJIMTJaIiBmaWxsPSIjMzMzIi8+CjwvZz4KPC9zdmc+" alt="Generated Thumbnail" id="generated-thumbnail">
                </div>

                <button class="btn-primary" id="download-btn">Download</button>

                <div class="view-history">
                    <a href="#" id="view-history-link">View History</a>
                </div>
            </div>
        </div>

        <!-- Welcome Back Card (for returning users) -->
        <div class="card" id="welcome-back-card" style="display: none;">
            <div class="card-header">
                <span class="brand">FREE THUMBNAIL GENERATOR</span>
            </div>
            <h2>Welcome Back!</h2>
            <p class="description">We found your previous thumbnail collection. You can generate new thumbnails immediately or upload new training images.</p>

            <div class="welcome-actions">
                <button class="btn-primary" id="continue-generating-btn">Continue Generating</button>
                <button class="btn-secondary" id="upload-new-btn">Upload New Thumbnails</button>
            </div>

            <div class="previous-thumbnails" id="previous-thumbnails">
                <h4>Your Previous Thumbnails:</h4>
                <div class="thumbnail-grid" id="previous-thumbnail-grid"></div>
            </div>
        </div>

        <!-- Progress Bar at Bottom -->
        <div class="bottom-progress">
            <div class="progress-step active" data-step="1">
                <div class="step-circle">1</div>
                <span>Upload</span>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step" data-step="2">
                <div class="step-circle">2</div>
                <span>Train</span>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step" data-step="3">
                <div class="step-circle">3</div>
                <span>Generate</span>
            </div>
            <div class="progress-line"></div>
            <div class="progress-step" data-step="4">
                <div class="step-circle">4</div>
                <span>Download</span>
            </div>
        </div>
    </div>

    <!-- API Setup Modal -->
    <div class="modal-overlay" id="api-modal" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3>🔑 Setup API Keys</h3>
                <button class="modal-close" id="modal-close">×</button>
            </div>
            <div class="modal-body">
                <p>To enable AI-powered thumbnail generation, please provide your API keys:</p>

                <div class="input-group">
                    <label for="gemini-api-key">Gemini API Key (Required for image analysis)</label>
                    <input type="password" id="gemini-api-key" placeholder="AIzaSy...">
                </div>

                <div class="input-group">
                    <label for="stability-api-key">Stability AI API Key (Required for thumbnail generation)</label>
                    <input type="password" id="stability-api-key" placeholder="sk-...">
                </div>

                <div class="api-help">
                    <p><strong>How to get your API keys:</strong></p>
                    <p>1. <strong>Gemini API:</strong> Get your key from <a href="https://makersuite.google.com/app/apikey" target="_blank">Google AI Studio</a></p>
                    <p>2. <strong>Stability AI:</strong> Get your key from <a href="https://platform.stability.ai/account/keys" target="_blank">Stability AI Platform</a></p>
                    <p>3. Your images will be stored locally in your browser</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="skip-api-btn">Skip (Demo Mode)</button>
                <button class="btn-primary" id="save-api-btn">Save & Continue</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
