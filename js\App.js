// Main App Component
const App = () => {
    // State management
    const [uploadedFiles, setUploadedFiles] = React.useState([]);
    const [imagePreviews, setImagePreviews] = React.useState([]);
    const [isAnalyzing, setIsAnalyzing] = React.useState(false);
    const [analysisProgress, setAnalysisProgress] = React.useState(0);
    const [currentStep, setCurrentStep] = React.useState(1);
    const [analysisComplete, setAnalysisComplete] = React.useState(false);
    const [styleProfile, setStyleProfile] = React.useState(null);
    const [videoTitle, setVideoTitle] = React.useState('');
    const [isGenerating, setIsGenerating] = React.useState(false);
    const [generationProgress, setGenerationProgress] = React.useState(0);
    const [generationStep, setGenerationStep] = React.useState(1);
    const [generatedThumbnails, setGeneratedThumbnails] = React.useState([]);
    const [apiKeys, setApiKeys] = React.useState({ gemini: '', replicate: '' });
    const [showApiSetup, setShowApiSetup] = React.useState(true);
    const [errors, setErrors] = React.useState([]);

    // Services
    const geminiService = React.useMemo(() => new GeminiService(), []);
    const replicateService = React.useMemo(() => new ReplicateService(), []);
    const fileHandler = React.useMemo(() => new FileHandler(), []);

    // Load saved data on component mount
    React.useEffect(() => {
        const savedApiKeys = fileHandler.loadFromLocalStorage('apiKeys');
        if (savedApiKeys) {
            setApiKeys(savedApiKeys);
            geminiService.setApiKey(savedApiKeys.gemini);
            replicateService.setApiKey(savedApiKeys.replicate);
            if (savedApiKeys.gemini && savedApiKeys.replicate) {
                setShowApiSetup(false);
            }
        }

        const savedStyleProfile = fileHandler.loadFromLocalStorage('styleProfile');
        if (savedStyleProfile) {
            setStyleProfile(savedStyleProfile);
            setAnalysisComplete(true);
            setCurrentStep(4);
        }
    }, []);

    // Handle file uploads
    const handleFilesUploaded = async (files) => {
        try {
            setUploadedFiles(files);
            const previews = await fileHandler.createImagePreviews(files);
            setImagePreviews(previews);
            setCurrentStep(2);
            setErrors([]);
        } catch (error) {
            setErrors(['Failed to process uploaded files. Please try again.']);
        }
    };

    // Remove uploaded file
    const handleRemoveFile = (index) => {
        const newFiles = uploadedFiles.filter((_, i) => i !== index);
        const newPreviews = imagePreviews.filter((_, i) => i !== index);
        setUploadedFiles(newFiles);
        setImagePreviews(newPreviews);
        
        if (newFiles.length < 5) {
            setCurrentStep(1);
            setAnalysisComplete(false);
            setStyleProfile(null);
        }
    };

    // Handle AI analysis
    const handleAnalyze = async () => {
        if (!apiKeys.gemini) {
            setErrors(['Please set your Gemini API key first.']);
            return;
        }

        setIsAnalyzing(true);
        setAnalysisProgress(0);
        setCurrentStep(2);
        setErrors([]);

        try {
            // Simulate progress updates
            const progressInterval = setInterval(() => {
                setAnalysisProgress(prev => {
                    if (prev >= 90) {
                        clearInterval(progressInterval);
                        return 90;
                    }
                    return prev + Math.random() * 15;
                });
            }, 1000);

            const profile = await geminiService.analyzeImages(uploadedFiles);
            
            clearInterval(progressInterval);
            setAnalysisProgress(100);
            setStyleProfile(profile);
            setAnalysisComplete(true);
            setCurrentStep(4);
            
            // Save to localStorage
            fileHandler.saveToLocalStorage('styleProfile', profile);
            
        } catch (error) {
            console.error('Analysis failed:', error);
            setErrors([`Analysis failed: ${error.message}`]);
        } finally {
            setIsAnalyzing(false);
        }
    };

    // Handle thumbnail generation
    const handleGenerate = async () => {
        if (!apiKeys.replicate || !apiKeys.gemini) {
            setErrors(['Please set both Gemini and Replicate API keys.']);
            return;
        }

        setIsGenerating(true);
        setGenerationProgress(0);
        setGenerationStep(1);
        setErrors([]);

        try {
            // Step 1: Generate prompt with Gemini
            setGenerationProgress(10);
            const prompt = await geminiService.generateThumbnailPrompt(videoTitle, styleProfile);
            
            setGenerationStep(2);
            setGenerationProgress(30);
            
            // Step 2: Generate thumbnails with Replicate
            const thumbnails = await replicateService.generateMultipleThumbnails(prompt, styleProfile, 3);
            
            setGenerationStep(3);
            setGenerationProgress(90);
            
            // Step 3: Process results
            setGeneratedThumbnails(thumbnails);
            setGenerationProgress(100);
            
            // Save to history
            const historyItem = {
                videoTitle,
                thumbnails,
                timestamp: new Date().toISOString(),
                styleProfile: styleProfile
            };
            
            const history = fileHandler.loadFromLocalStorage('thumbnailHistory') || [];
            history.unshift(historyItem);
            fileHandler.saveToLocalStorage('thumbnailHistory', history.slice(0, 50)); // Keep last 50
            
        } catch (error) {
            console.error('Generation failed:', error);
            setErrors([`Generation failed: ${error.message}`]);
        } finally {
            setIsGenerating(false);
        }
    };

    // Handle API key setup
    const handleApiKeySubmit = (keys) => {
        setApiKeys(keys);
        geminiService.setApiKey(keys.gemini);
        replicateService.setApiKey(keys.replicate);
        fileHandler.saveToLocalStorage('apiKeys', keys);
        setShowApiSetup(false);
    };

    // API Setup Modal
    const ApiSetupModal = () => {
        const [tempKeys, setTempKeys] = React.useState(apiKeys);

        return React.createElement('div', {
            style: {
                position: 'fixed',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: 'rgba(0, 0, 0, 0.8)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                zIndex: 1000,
                padding: '20px'
            }
        },
            React.createElement('div', {
                style: {
                    background: 'white',
                    borderRadius: '15px',
                    padding: '30px',
                    maxWidth: '500px',
                    width: '100%'
                }
            },
                React.createElement('h2', { style: { marginBottom: '20px' } }, '🔑 API Setup'),
                React.createElement('p', { 
                    style: { marginBottom: '20px', color: '#666' } 
                }, 'Enter your API keys to get started:'),
                
                React.createElement('div', { className: 'input-group' },
                    React.createElement('label', { className: 'input-label' }, 'Gemini API Key'),
                    React.createElement('input', {
                        type: 'password',
                        className: 'text-input',
                        placeholder: 'Enter your Gemini API key',
                        value: tempKeys.gemini,
                        onChange: (e) => setTempKeys({...tempKeys, gemini: e.target.value})
                    })
                ),
                
                React.createElement('div', { className: 'input-group' },
                    React.createElement('label', { className: 'input-label' }, 'Replicate API Key'),
                    React.createElement('input', {
                        type: 'password',
                        className: 'text-input',
                        placeholder: 'Enter your Replicate API key',
                        value: tempKeys.replicate,
                        onChange: (e) => setTempKeys({...tempKeys, replicate: e.target.value})
                    })
                ),
                
                React.createElement('div', {
                    style: { 
                        display: 'flex', 
                        gap: '10px', 
                        justifyContent: 'flex-end',
                        marginTop: '20px'
                    }
                },
                    React.createElement('button', {
                        className: 'btn-secondary',
                        onClick: () => setShowApiSetup(false)
                    }, 'Skip for now'),
                    React.createElement('button', {
                        className: 'btn',
                        onClick: () => handleApiKeySubmit(tempKeys),
                        disabled: !tempKeys.gemini || !tempKeys.replicate
                    }, 'Save & Continue')
                )
            )
        );
    };

    return React.createElement('div', { className: 'container' },
        // Header
        React.createElement('div', { className: 'header' },
            React.createElement('h1', null, '🎨 Personalized Thumbnail Creator'),
            React.createElement('p', null, 'AI-powered thumbnails that match your unique style')
        ),

        // Error Messages
        errors.length > 0 && React.createElement('div', null,
            errors.map((error, index) =>
                React.createElement('div', {
                    key: index,
                    className: 'error-message'
                }, error)
            )
        ),

        // API Setup Button
        React.createElement('div', {
            style: { textAlign: 'center', marginBottom: '20px' }
        },
            React.createElement('button', {
                className: 'btn-secondary',
                onClick: () => setShowApiSetup(true)
            }, '⚙️ API Settings')
        ),

        // Upload Section
        React.createElement(UploadSection, {
            onFilesUploaded: handleFilesUploaded,
            uploadedFiles: imagePreviews,
            onRemoveFile: handleRemoveFile,
            isAnalyzing: isAnalyzing
        }),

        // Analysis Progress
        React.createElement(AnalysisProgress, {
            isAnalyzing: isAnalyzing,
            progress: analysisProgress,
            currentStep: currentStep,
            onAnalyze: handleAnalyze,
            canAnalyze: uploadedFiles.length >= 5 && !isAnalyzing,
            analysisComplete: analysisComplete,
            styleProfile: styleProfile
        }),

        // Title Input
        React.createElement(TitleInput, {
            videoTitle: videoTitle,
            onTitleChange: setVideoTitle,
            onGenerate: handleGenerate,
            isGenerating: isGenerating,
            canGenerate: analysisComplete && videoTitle.trim().length >= 5 && !isGenerating,
            analysisComplete: analysisComplete
        }),

        // Thumbnail Generator
        React.createElement(ThumbnailGenerator, {
            isGenerating: isGenerating,
            generationProgress: generationProgress,
            generationStep: generationStep
        }),

        // Results Display
        React.createElement(ResultsDisplay, {
            generatedThumbnails: generatedThumbnails,
            videoTitle: videoTitle,
            onRegenerate: handleGenerate,
            isRegenerating: isGenerating
        }),

        // API Setup Modal
        showApiSetup && React.createElement(ApiSetupModal)
    );
};

// Render the app
ReactDOM.render(React.createElement(App), document.getElementById('root'));
