// Thumbnail Generator Component
const ThumbnailGenerator = ({ 
    isGenerating, 
    generationProgress, 
    generationStep, 
    onRegenerateVariations,
    canRegenerate 
}) => {
    const generationSteps = [
        { id: 1, name: 'Creating Prompt', description: '<PERSON> is crafting the perfect prompt' },
        { id: 2, name: 'Generating Images', description: 'Replicate AI is creating your thumbnails' },
        { id: 3, name: 'Processing Results', description: 'Finalizing your personalized thumbnails' }
    ];

    const getStepStatus = (stepId) => {
        if (stepId < generationStep) return 'completed';
        if (stepId === generationStep) return 'active';
        return 'pending';
    };

    if (!isGenerating) {
        return null;
    }

    return React.createElement('div', { className: 'card' },
        React.createElement('h2', null, '🎨 Generating Your Thumbnails'),
        
        // Generation Steps
        React.createElement('div', { className: 'step-indicator' },
            generationSteps.map(step =>
                React.createElement('div', {
                    key: step.id,
                    className: `step ${getStepStatus(step.id)}`
                },
                    React.createElement('div', { className: 'step-number' }, step.id),
                    React.createElement('div', null,
                        React.createElement('div', { 
                            style: { fontWeight: '600', fontSize: '0.9rem' } 
                        }, step.name),
                        React.createElement('div', { 
                            style: { fontSize: '0.8rem', color: '#666' } 
                        }, step.description)
                    )
                )
            )
        ),

        // Progress Bar
        React.createElement('div', { className: 'progress-container' },
            React.createElement('div', { className: 'progress-bar' },
                React.createElement('div', {
                    className: 'progress-fill',
                    style: { width: `${generationProgress}%` }
                })
            ),
            React.createElement('div', { className: 'progress-text' },
                `${Math.round(generationProgress)}% Complete`
            )
        ),

        // Current Step Details
        React.createElement('div', {
            style: {
                textAlign: 'center',
                marginTop: '20px',
                padding: '20px',
                background: '#f8f9fa',
                borderRadius: '10px'
            }
        },
            React.createElement('div', { 
                style: { 
                    display: 'flex', 
                    justifyContent: 'center', 
                    marginBottom: '15px' 
                } 
            },
                React.createElement('div', { className: 'spinner' })
            ),
            React.createElement('h3', { 
                style: { 
                    marginBottom: '10px', 
                    color: '#2d3748' 
                } 
            }, 
                generationSteps[generationStep - 1]?.name || 'Processing...'
            ),
            React.createElement('p', { 
                style: { 
                    color: '#666', 
                    fontSize: '0.95rem' 
                } 
            },
                getStepMessage(generationStep)
            )
        ),

        // Estimated Time
        React.createElement('div', {
            style: {
                textAlign: 'center',
                marginTop: '15px',
                fontSize: '0.9rem',
                color: '#666'
            }
        },
            React.createElement('span', null, '⏱️ Estimated time: '),
            React.createElement('strong', null, getEstimatedTime(generationStep))
        ),

        // Tips while waiting
        React.createElement('div', {
            style: {
                marginTop: '25px',
                padding: '15px',
                background: '#e6fffa',
                borderRadius: '8px'
            }
        },
            React.createElement('h4', { 
                style: { 
                    marginBottom: '10px', 
                    color: '#234e52',
                    fontSize: '1rem'
                } 
            }, '💡 Did you know?'),
            React.createElement('p', {
                style: {
                    margin: 0,
                    color: '#2c7a7b',
                    fontSize: '0.9rem',
                    lineHeight: '1.5'
                }
            }, getRandomTip())
        )
    );

    function getStepMessage(step) {
        switch (step) {
            case 1:
                return 'Our AI is analyzing your video title and creating a detailed prompt based on your unique style profile...';
            case 2:
                return 'High-quality thumbnails are being generated using advanced AI models. This may take a few moments...';
            case 3:
                return 'Almost done! We\'re applying final touches and preparing your thumbnails for download...';
            default:
                return 'Processing your request...';
        }
    }

    function getEstimatedTime(step) {
        switch (step) {
            case 1:
                return '30-60 seconds';
            case 2:
                return '2-3 minutes';
            case 3:
                return '15-30 seconds';
            default:
                return 'A few minutes';
        }
    }

    function getRandomTip() {
        const tips = [
            'Thumbnails with faces get 30% more clicks than those without!',
            'Bright, contrasting colors help your thumbnail stand out in search results.',
            'The best thumbnails tell a story in a single glance.',
            'Text should take up no more than 30% of your thumbnail space.',
            'Emotions in thumbnails can increase click-through rates by up to 40%.',
            'Testing different thumbnail styles can help you find what works best for your audience.',
            'Consistency in thumbnail style helps build brand recognition.',
            'The rule of thirds applies to thumbnails too - place key elements along the grid lines!'
        ];
        return tips[Math.floor(Math.random() * tips.length)];
    }
};

window.ThumbnailGenerator = ThumbnailGenerator;
