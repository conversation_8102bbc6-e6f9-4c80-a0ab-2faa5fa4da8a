<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personalized Thumbnail Creator - React Version</title>
    <link rel="stylesheet" href="styles.css">
    
    <!-- React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- AI Services -->
    <script src="js/services/geminiService.js"></script>
    <script src="js/services/stabilityService.js"></script>
    
    <!-- Utility Services -->
    <script src="js/utils/fileHandling.js"></script>
</head>
<body>
    <div id="root"></div>
    
    <!-- React Components -->
    <script src="js/components/UploadSection.js"></script>
    <script src="js/components/AnalysisProgress.js"></script>
    <script src="js/components/TitleInput.js"></script>
    <script src="js/components/ThumbnailGenerator.js"></script>
    <script src="js/components/ResultsDisplay.js"></script>
    
    <!-- Main React App -->
    <script src="js/App.js"></script>
</body>
</html>
