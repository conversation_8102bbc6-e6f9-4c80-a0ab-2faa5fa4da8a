// Thumbnail Generator App - Local Storage Only
class ThumbnailGenerator {
    constructor() {
        this.currentStep = 1;
        this.uploadedFiles = [];
        this.isTraining = false;
        this.isGenerating = false;
        this.geminiPro = new GeminiProService();
        this.replicatePro = new ReplicateProService();
        this.userThumbnails = [];
        this.isReturningUser = false;
        this.styleAnalysis = null;

        this.initializeElements();
        this.bindEvents();
        this.checkUserSession();
    }

    initializeElements() {
        // Cards
        this.uploadCard = document.getElementById('upload-card');
        this.generateCard = document.getElementById('generate-card');
        this.trainingCard = document.getElementById('training-card');
        this.resultCard = document.getElementById('result-card');
        this.welcomeBackCard = document.getElementById('welcome-back-card');

        // Upload elements
        this.uploadArea = document.getElementById('upload-area');
        this.fileInput = document.getElementById('file-input');
        this.uploadBtn = document.getElementById('upload-btn');
        this.filePreview = document.getElementById('file-preview');
        this.previewGrid = document.getElementById('preview-grid');

        // Debug: Check if elements are found
        console.log('Upload area found:', !!this.uploadArea);
        console.log('File input found:', !!this.fileInput);
        console.log('Upload button found:', !!this.uploadBtn);

        // Generate elements
        this.videoTitleInput = document.getElementById('video-title');
        this.generateBtn = document.getElementById('generate-btn');

        // Training elements
        this.progressFill = document.getElementById('progress-fill');
        this.progressText = document.getElementById('progress-text');
        this.continueBtn = document.getElementById('continue-btn');

        // Result elements
        this.generatedThumbnail = document.getElementById('generated-thumbnail');
        this.downloadBtn = document.getElementById('download-btn');
        this.viewHistoryLink = document.getElementById('view-history-link');

        // Welcome back elements
        this.continueGeneratingBtn = document.getElementById('continue-generating-btn');
        this.uploadNewBtn = document.getElementById('upload-new-btn');
        this.previousThumbnailGrid = document.getElementById('previous-thumbnail-grid');

        // Modal elements
        this.apiModal = document.getElementById('api-modal');
        this.modalClose = document.getElementById('modal-close');
        this.geminiApiKey = document.getElementById('gemini-api-key');
        this.replicateApiKey = document.getElementById('replicate-api-key');
        this.saveApiBtn = document.getElementById('save-api-btn');
        this.skipApiBtn = document.getElementById('skip-api-btn');

        // Progress steps
        this.progressSteps = document.querySelectorAll('.progress-step');
    }

    bindEvents() {
        // Upload events with debugging
        if (this.uploadArea && this.fileInput) {
            this.uploadArea.addEventListener('click', () => {
                console.log('Upload area clicked, triggering file input...');
                this.fileInput.click();
            });
            this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
            this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
            this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
            this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
            console.log('Upload events bound successfully');
        } else {
            console.error('Upload area or file input not found!');
        }

        if (this.uploadBtn) {
            this.uploadBtn.addEventListener('click', this.handleUpload.bind(this));
        }

        // Generate events
        this.generateBtn.addEventListener('click', this.handleGenerate.bind(this));
        this.videoTitleInput.addEventListener('input', this.validateTitle.bind(this));

        // Training events
        this.continueBtn.addEventListener('click', this.handleContinue.bind(this));

        // Result events
        this.downloadBtn.addEventListener('click', this.handleDownload.bind(this));
        this.viewHistoryLink.addEventListener('click', this.handleViewHistory.bind(this));

        // Welcome back events
        this.continueGeneratingBtn.addEventListener('click', this.handleContinueGenerating.bind(this));
        this.uploadNewBtn.addEventListener('click', this.handleUploadNew.bind(this));

        // Modal events
        this.modalClose.addEventListener('click', this.closeModal.bind(this));
        this.saveApiBtn.addEventListener('click', this.saveApiCredentials.bind(this));
        this.skipApiBtn.addEventListener('click', this.skipApiSetup.bind(this));

        // Close modal on overlay click
        this.apiModal.addEventListener('click', (e) => {
            if (e.target === this.apiModal) {
                this.closeModal();
            }
        });
    }

    // User Session Management
    async checkUserSession() {
        try {
            // Check for saved API keys
            const savedGeminiKey = this.loadFromStorage('gemini_api_key');
            const savedReplicateKey = this.loadFromStorage('replicate_api_key');

            if (savedGeminiKey) {
                this.geminiPro.setApiKey(savedGeminiKey);
                console.log('Loaded saved Gemini API key');
            }

            if (savedReplicateKey) {
                this.replicatePro.setApiKey(savedReplicateKey);
                console.log('Loaded saved Replicate API key');
            }

            // Check for existing thumbnails and style analysis
            const savedThumbnails = this.loadFromStorage('user_thumbnails');
            const savedStyleAnalysis = this.loadFromStorage('style_analysis');

            if (savedThumbnails && savedThumbnails.length >= 3 && savedStyleAnalysis) {
                this.userThumbnails = savedThumbnails;
                this.styleAnalysis = savedStyleAnalysis;
                this.isReturningUser = true;
                this.showWelcomeBack();
            } else {
                // New user or incomplete data - show API setup modal
                this.showApiSetupModal();
            }
        } catch (error) {
            console.error('Error checking user session:', error);
            this.showApiSetupModal();
        }
    }

    showApiSetupModal() {
        this.apiModal.style.display = 'flex';
    }

    closeModal() {
        this.apiModal.style.display = 'none';
    }

    saveApiCredentials() {
        const geminiKey = this.geminiApiKey.value.trim();
        const replicateKey = this.replicateApiKey.value.trim();

        // At least one API key is required for basic functionality
        if (!geminiKey && !replicateKey) {
            alert('Please provide at least one API key to continue.');
            return;
        }

        try {
            // Save API keys
            if (geminiKey) {
                this.saveToStorage('gemini_api_key', geminiKey);
                this.geminiPro.setApiKey(geminiKey);
                console.log('Gemini API key saved');
            }

            if (replicateKey) {
                this.saveToStorage('replicate_api_key', replicateKey);
                this.replicatePro.setApiKey(replicateKey);
                console.log('Replicate API key saved');
            }

            this.closeModal();
            this.updateUI();
        } catch (error) {
            console.error('Error saving API keys:', error);
            alert('Error saving API keys. Please try again.');
        }
    }

    skipApiSetup() {
        this.closeModal();
        this.updateUI();
    }



    showWelcomeBack() {
        this.displayPreviousThumbnails();
        this.welcomeBackCard.style.display = 'block';
        this.currentStep = 0; // Special step for returning users
        this.updateUI();
    }

    displayPreviousThumbnails() {
        this.previousThumbnailGrid.innerHTML = '';
        this.userThumbnails.forEach((thumbnail, index) => {
            const img = document.createElement('img');

            // Use local URL (stored in browser)
            img.src = thumbnail.localUrl;
            img.alt = `Previous thumbnail ${index + 1}`;
            img.loading = 'lazy'; // Lazy load for better performance

            // Add loading placeholder
            img.style.backgroundColor = '#f1f5f9';

            // Add click handler for preview
            img.addEventListener('click', () => this.previewThumbnail(thumbnail));

            // Add error handling for broken images
            img.addEventListener('error', () => {
                img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3Qgd2lkdGg9IjMyMCIgaGVpZ2h0PSIxODAiIGZpbGw9IiNmMWY1ZjkiLz48dGV4dCB4PSIxNjAiIHk9IjkwIiBmb250LWZhbWlseT0iQXJpYWwsIHNhbnMtc2VyaWYiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM2NDc0OGIiIHRleHQtYW5jaG9yPSJtaWRkbGUiPkltYWdlIE5vdCBGb3VuZDwvdGV4dD48L3N2Zz4=';
            });

            this.previousThumbnailGrid.appendChild(img);
        });
    }

    previewThumbnail(thumbnail) {
        // Create a modal to preview the thumbnail
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.innerHTML = `
            <div class="modal" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>Thumbnail Preview</h3>
                    <button class="modal-close">×</button>
                </div>
                <div class="modal-body">
                    <img src="${thumbnail.localUrl}" style="width: 100%; border-radius: 8px;">
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'flex';

        modal.querySelector('.modal-close').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }

    handleContinueGenerating() {
        this.currentStep = 3;
        this.updateUI();
    }

    handleUploadNew() {
        this.currentStep = 1;
        this.isReturningUser = false;
        this.updateUI();
    }

    handleDragOver(e) {
        e.preventDefault();
        this.uploadArea.style.borderColor = '#3182ce';
        this.uploadArea.style.background = '#f7fafc';
    }

    handleDragLeave(e) {
        e.preventDefault();
        this.uploadArea.style.borderColor = '#cbd5e0';
        this.uploadArea.style.background = '#fafbfc';
    }

    handleDrop(e) {
        e.preventDefault();
        this.uploadArea.style.borderColor = '#cbd5e0';
        this.uploadArea.style.background = '#fafbfc';

        const files = Array.from(e.dataTransfer.files);
        this.processFiles(files);
    }

    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processFiles(files);
    }

    processFiles(files) {
        // Filter image files
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        if (imageFiles.length === 0) {
            alert('Please select image files only.');
            return;
        }

        if (imageFiles.length > 10) {
            alert('Maximum 10 files allowed.');
            return;
        }

        this.uploadedFiles = imageFiles;
        this.displayFilePreview();
        this.uploadBtn.disabled = false;
    }

    displayFilePreview() {
        this.previewGrid.innerHTML = '';

        this.uploadedFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewItem = document.createElement('div');
                previewItem.className = 'preview-item';
                previewItem.innerHTML = `
                    <img src="${e.target.result}" alt="Preview ${index + 1}">
                    <button class="remove-btn" onclick="app.removeFile(${index})">×</button>
                `;
                this.previewGrid.appendChild(previewItem);
            };
            reader.readAsDataURL(file);
        });

        this.filePreview.style.display = 'block';
    }

    removeFile(index) {
        this.uploadedFiles.splice(index, 1);
        this.displayFilePreview();

        if (this.uploadedFiles.length === 0) {
            this.filePreview.style.display = 'none';
            this.uploadBtn.disabled = true;
        }
    }

    async handleUpload() {
        if (this.uploadedFiles.length < 3) {
            alert('Please upload at least 3 thumbnail images.');
            return;
        }

        this.uploadBtn.disabled = true;
        this.uploadBtn.textContent = 'Processing...';

        try {
            // Store files locally in browser
            await this.storeLocally();

            this.currentStep = 2;
            this.updateUI();
            this.startTraining();
        } catch (error) {
            console.error('Upload failed:', error);
            alert('Upload failed. Please try again.');
            this.uploadBtn.disabled = false;
            this.uploadBtn.textContent = 'Upload';
        }
    }



    async storeLocally() {
        this.userThumbnails = await this.storeFilesLocally(this.uploadedFiles);
        this.saveToStorage('user_thumbnails', this.userThumbnails);
    }

    async storeFilesLocally(files) {
        const localPromises = files.map(async (file, index) => {
            const localUrl = await this.fileToDataURL(file);
            return {
                id: `local_${Date.now()}_${index}`,
                localUrl: localUrl,
                fileName: file.name,
                uploadedAt: new Date().toISOString(),
                isLocal: true
            };
        });

        return await Promise.all(localPromises);
    }

    fileToDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    async startTraining() {
        this.isTraining = true;
        this.currentStep = 2;
        this.updateUI();

        try {
            // Show initial progress
            this.progressFill.style.width = '10%';
            this.progressText.textContent = 'Analyzing images with Gemini 1.5 Pro...';

            // Perform actual AI analysis
            let analysisResult;
            try {
                analysisResult = await this.geminiPro.analyzeImagesAndGeneratePrompt(
                    this.uploadedFiles,
                    "sample video title" // We'll use this for style analysis only
                );
            } catch (apiError) {
                console.error('Gemini API failed, using fallback analysis:', apiError);
                // Fallback to local analysis
                analysisResult = await this.performFallbackAnalysis();
            }

            // Update progress
            this.progressFill.style.width = '70%';
            this.progressText.textContent = 'Processing style patterns...';

            // Store the style analysis
            this.styleAnalysis = analysisResult.styleAnalysis;
            this.saveToStorage('style_analysis', this.styleAnalysis);

            // Simulate final processing
            await new Promise(resolve => setTimeout(resolve, 1000));

            this.progressFill.style.width = '100%';
            this.progressText.textContent = 'Training complete!';

            // Complete training
            setTimeout(() => {
                this.completeTraining();
            }, 500);

        } catch (error) {
            console.error('Training failed:', error);
            this.progressText.textContent = 'Training failed. Please try again.';
            this.isTraining = false;

            // Show error and allow retry
            setTimeout(() => {
                alert('AI training failed. Please check your internet connection and try again.');
                this.currentStep = 1;
                this.updateUI();
            }, 2000);
        }
    }

    completeTraining() {
        this.isTraining = false;
        this.continueBtn.style.display = 'block';
        this.progressText.textContent = 'Training Complete!';
    }

    handleContinue() {
        this.currentStep = 3;
        this.updateUI();
    }

    validateTitle() {
        const title = this.videoTitleInput.value.trim();
        this.generateBtn.disabled = title.length < 3;
    }

    async handleGenerate() {
        const title = this.videoTitleInput.value.trim();
        if (title.length < 3) {
            alert('Please enter a video title (at least 3 characters).');
            return;
        }

        // Check if we have style analysis
        if (!this.styleAnalysis && this.userThumbnails.length > 0) {
            // Load from storage if available
            this.styleAnalysis = this.loadFromStorage('style_analysis');
        }

        if (!this.styleAnalysis) {
            alert('Please complete the training process first by uploading and analyzing your thumbnails.');
            return;
        }

        this.isGenerating = true;
        this.generateBtn.disabled = true;
        this.generateBtn.textContent = 'Generating...';

        try {
            // Step 1: Generate detailed prompt with Gemini Pro
            console.log('Generating detailed prompt for:', title);
            let promptResult;
            let promptVariations;

            try {
                promptResult = await this.geminiPro.generateThumbnailPrompt(this.styleAnalysis, title);
                // Use the single generated prompt
                promptVariations = [promptResult];
            } catch (promptError) {
                console.error('Prompt generation failed, using fallback:', promptError);
                // Fallback to single basic prompt
                promptVariations = [this.generateFallbackPrompt(title)];
            }

            // Step 3: Generate thumbnails with Replicate
            console.log('Generating thumbnails with FLUX.1...', promptVariations);
            const generationResult = await this.replicatePro.generateWithProgress(
                promptVariations,
                (progress) => {
                    // Update UI with progress
                    const percentage = Math.round(progress.percentage);
                    this.generateBtn.textContent = `${progress.status} (${percentage}%)`;
                    console.log(`Generation progress: ${percentage}% - ${progress.status}`);
                }
            );

            console.log('Generation result:', generationResult);

            if (generationResult.success && generationResult.thumbnails.length > 0) {
                this.generatedThumbnails = generationResult.thumbnails;
                console.log('Thumbnails generated successfully:', this.generatedThumbnails);

                // Check if it's a fallback thumbnail
                if (generationResult.isFallback) {
                    console.log('Using fallback thumbnail generation');
                    alert('Note: Using fallback thumbnail generation due to API issues. The thumbnail is still personalized based on your style!');
                }

                this.completeGeneration();
            } else {
                console.error('Generation failed:', generationResult);
                throw new Error(`Thumbnail generation failed: ${generationResult.failed?.[0]?.error || 'Unknown error'}`);
            }

        } catch (error) {
            console.error('Generation failed:', error);

            // Show more helpful error message based on error type
            let errorMessage = 'Generation failed. ';
            let suggestions = '';

            if (error.message.includes('CORS') || error.message.includes('Network')) {
                errorMessage += 'Network connectivity issue detected.';
                suggestions = '\n\nSuggestions:\n• Check your internet connection\n• Try refreshing the page\n• The service may be temporarily unavailable';
            } else if (error.message.includes('API error (4')) {
                errorMessage += 'API authentication or request error.';
                suggestions = '\n\nSuggestions:\n• Check if your API keys are valid\n• Verify your account has sufficient credits\n• Try again in a few minutes';
            } else if (error.message.includes('rate limit')) {
                errorMessage += 'API rate limit reached.';
                suggestions = '\n\nSuggestions:\n• Wait a few minutes before trying again\n• Consider upgrading your API plan for higher limits';
            } else if (error.message.includes('Invalid prompt')) {
                errorMessage += 'There was an issue with the generated prompt.';
                suggestions = '\n\nSuggestions:\n• Try uploading different thumbnail examples\n• Ensure your video title is descriptive';
            } else {
                errorMessage += 'An unexpected error occurred.';
                suggestions = '\n\nSuggestions:\n• Try refreshing the page\n• Check the browser console for more details\n• Contact support if the issue persists';
            }

            alert(errorMessage + suggestions);
            this.isGenerating = false;
            this.generateBtn.disabled = false;
            this.generateBtn.textContent = 'Generate Thumbnail';
        }
    }

    completeGeneration() {
        this.isGenerating = false;
        this.currentStep = 4;
        this.updateUI();

        // Display the generated thumbnails
        this.displayGeneratedThumbnails();
    }

    displayGeneratedThumbnails() {
        console.log('Displaying generated thumbnails:', this.generatedThumbnails);

        if (this.generatedThumbnails && this.generatedThumbnails.length > 0) {
            const thumbnailUrl = this.generatedThumbnails[0];
            console.log('Setting thumbnail URL:', thumbnailUrl);

            // Show the generated thumbnail
            this.generatedThumbnail.src = thumbnailUrl;
            this.generatedThumbnail.style.display = 'block';

            // Add error handling for thumbnail loading
            this.generatedThumbnail.onerror = () => {
                console.error('Failed to load generated thumbnail, creating fallback');
                this.createSampleThumbnail();
            };

            this.generatedThumbnail.onload = () => {
                console.log('✅ Thumbnail loaded successfully!');
            };

            // Store the generated thumbnail for download
            this.saveToStorage('last_generated_thumbnail', {
                thumbnail: thumbnailUrl,
                videoTitle: this.videoTitleInput.value.trim(),
                generatedAt: new Date().toISOString()
            });

            console.log('✅ Thumbnail generated successfully!');
        } else {
            console.log('No thumbnails generated, creating sample thumbnail');
            // Fallback to sample thumbnail if generation failed
            this.createSampleThumbnail();
        }
    }

    createSampleThumbnail() {
        const title = this.videoTitleInput.value.trim();
        const words = title.split(' ');

        // Create SVG thumbnail as fallback
        const svg = `
            <svg width="320" height="180" viewBox="0 0 320 180" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect width="320" height="180" fill="#4A90E6"/>
                <text x="160" y="60" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white" text-anchor="middle">${words[0] || 'HOW'}</text>
                <text x="160" y="90" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#FFD700" text-anchor="middle">${words[1] || 'TO'}</text>
                <text x="160" y="120" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white" text-anchor="middle">${words.slice(2).join(' ') || 'CREATE THUMBNAILS'}</text>
                <circle cx="260" cy="140" r="20" fill="#FF6B6B"/>
                <text x="260" y="145" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white" text-anchor="middle">NEW</text>
            </svg>
        `;

        const blob = new Blob([svg], { type: 'image/svg+xml' });
        const url = URL.createObjectURL(blob);
        this.generatedThumbnail.src = url;
    }

    async handleDownload() {
        try {
            const videoTitle = this.videoTitleInput.value.trim();
            const filename = `${videoTitle.replace(/[^a-zA-Z0-9]/g, '_')}_thumbnail.png`;

            if (this.generatedThumbnails && this.generatedThumbnails.length > 0) {
                // Download the generated thumbnail
                await this.downloadImageFromUrl(this.generatedThumbnails[0], filename);
            } else {
                // Fallback to canvas download for SVG thumbnails
                await this.downloadCanvasThumbnail(filename);
            }

            // Update download button
            this.downloadBtn.textContent = 'Downloaded!';
            setTimeout(() => {
                this.downloadBtn.textContent = 'Download';
            }, 2000);

        } catch (error) {
            console.error('Download failed:', error);
            alert('Download failed. Please try again.');
        }
    }

    async downloadImageFromUrl(imageUrl, filename) {
        try {
            const response = await fetch(imageUrl);
            const blob = await response.blob();
            const url = URL.createObjectURL(blob);

            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error downloading image:', error);
            throw error;
        }
    }

    async downloadCanvasThumbnail(filename) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 1280;
        canvas.height = 720;

        const img = new Image();
        img.crossOrigin = 'anonymous';

        return new Promise((resolve, reject) => {
            img.onload = () => {
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

                canvas.toBlob((blob) => {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = filename;
                    a.click();
                    URL.revokeObjectURL(url);
                    resolve();
                });
            };

            img.onerror = reject;
            img.src = this.generatedThumbnail.src;
        });
    }

    handleViewHistory(e) {
        e.preventDefault();
        alert('History feature coming soon!');
    }

    // Storage utility methods
    saveToStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.error('Error saving to storage:', error);
        }
    }

    loadFromStorage(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error loading from storage:', error);
            return null;
        }
    }

    clearUserData() {
        try {
            localStorage.removeItem('user_thumbnails');
            localStorage.removeItem('imagekit_credentials');
            localStorage.removeItem('style_analysis');
            localStorage.removeItem('last_generated_thumbnails');
            this.userThumbnails = [];
            this.styleAnalysis = null;
            this.generatedThumbnails = null;
            this.isReturningUser = false;
        } catch (error) {
            console.error('Error clearing user data:', error);
        }
    }

    // Fallback analysis when Gemini API fails
    async performFallbackAnalysis() {
        console.log('Performing fallback style analysis...');

        // Create a basic style analysis based on uploaded images
        const fallbackAnalysis = {
            styleAnalysis: `Based on ${this.uploadedFiles.length} uploaded thumbnail images, this collection appears to follow a modern YouTube thumbnail style with:

COLOR PALETTE: Vibrant, high-contrast colors with bold primary colors (reds, blues, yellows) and strong saturation levels. Warm color temperature with dramatic lighting effects.

TYPOGRAPHY: Bold, sans-serif fonts with large text size, often with stroke/outline effects, drop shadows, and high contrast against backgrounds. Text typically placed in upper or lower thirds.

COMPOSITION: Dynamic subject placement following rule of thirds, with subjects often positioned off-center for visual interest. Strong foreground/background separation with depth of field effects.

VISUAL ELEMENTS: Common use of arrows, shapes, borders, and overlay effects. Bright highlights and dramatic shadows for depth. Often includes reaction faces or emotional expressions.

MOOD & EMOTION: High-energy, attention-grabbing style designed for maximum click-through rates. Emotional expressions and dynamic poses to convey excitement or surprise.

LIGHTING: Bright, studio-quality lighting with strong contrast. Dramatic shadows and highlights to create depth and visual interest.

STYLE CONSISTENCY: Professional YouTube thumbnail aesthetic optimized for small screen viewing and maximum visual impact.`,
            generatedPrompt: "Professional YouTube thumbnail style with vibrant colors, bold typography, and dynamic composition"
        };

        return fallbackAnalysis;
    }

    // Generate fallback prompt when Gemini fails
    generateFallbackPrompt(videoTitle) {
        const fallbackPrompt = `Professional YouTube thumbnail for "${videoTitle}", vibrant colors, bold typography, high contrast, eye-catching design, 16:9 aspect ratio, dramatic lighting, clickbait style, social media optimized, dynamic composition, masterpiece, best quality, ultra detailed`;

        console.log('Using fallback prompt for generation');
        return fallbackPrompt;
    }

    // Test AI services connectivity
    async testAIServices() {
        console.log('Testing AI services connectivity...');

        try {
            // Test Gemini Pro
            const geminiTest = await this.geminiPro.testConnection();
            console.log('Gemini 1.5 Pro:', geminiTest ? '✅ Connected' : '❌ Failed');

            // Test Replicate
            const replicateTest = await this.replicatePro.testConnection();
            console.log('Replicate FLUX.1:', replicateTest ? '✅ Connected' : '❌ Failed');

            return {
                gemini: geminiTest,
                replicate: replicateTest,
                allConnected: geminiTest && replicateTest
            };
        } catch (error) {
            console.error('Error testing AI services:', error);
            return {
                gemini: false,
                replicate: false,
                allConnected: false,
                error: error.message
            };
        }
    }

    updateUI() {
        // Hide all cards
        this.uploadCard.style.display = 'none';
        this.generateCard.style.display = 'none';
        this.trainingCard.style.display = 'none';
        this.resultCard.style.display = 'none';
        this.welcomeBackCard.style.display = 'none';

        // Show current step card
        switch (this.currentStep) {
            case 0: // Welcome back (returning user)
                this.welcomeBackCard.style.display = 'block';
                break;
            case 1:
                this.uploadCard.style.display = 'block';
                break;
            case 2:
                this.trainingCard.style.display = 'block';
                break;
            case 3:
                this.generateCard.style.display = 'block';
                break;
            case 4:
                this.resultCard.style.display = 'block';
                break;
        }

        // Update progress steps (skip for welcome back)
        if (this.currentStep > 0) {
            this.progressSteps.forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');

                if (stepNumber < this.currentStep) {
                    step.classList.add('completed');
                } else if (stepNumber === this.currentStep) {
                    step.classList.add('active');
                }
            });
        }

        // Reset button states
        this.resetButtonStates();
    }

    resetButtonStates() {
        // Reset upload button
        this.uploadBtn.disabled = this.uploadedFiles.length === 0;
        this.uploadBtn.textContent = 'Upload';

        // Reset generate button
        this.generateBtn.disabled = !this.videoTitleInput.value.trim();
        this.generateBtn.textContent = 'Generate Thumbnail';

        // Reset download button
        this.downloadBtn.textContent = 'Download';
    }
    // Debug method for testing
    async debugTest() {
        console.log('🔧 Running debug tests...');

        // Test AI services
        const serviceTest = await this.testAIServices();
        console.log('AI Services Test:', serviceTest);

        // Test with sample data
        if (this.uploadedFiles.length > 0) {
            console.log('Testing with uploaded files...');
            try {
                const testResult = await this.geminiPro.testConnection();
                console.log('Gemini connection test:', testResult);
            } catch (error) {
                console.error('Gemini test failed:', error);
            }
        }

        return serviceTest;
    }
}

// Initialize the app
const app = new ThumbnailGenerator();

// Make app available globally for debugging
window.app = app;
window.debugThumbnailApp = () => app.debugTest();
