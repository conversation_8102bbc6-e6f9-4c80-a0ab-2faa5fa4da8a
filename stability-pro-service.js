// Enhanced Stability AI Service for High-Quality Thumbnail Generation
class StabilityProService {
    constructor() {
        this.apiKey = null; // Will be set by user or from environment
        this.baseUrl = 'https://api.stability.ai/v2beta';

        // Default model configuration
        this.defaultModel = 'stable-image-ultra';
        this.currentModel = this.defaultModel;

        // Supported models
        this.models = {
            'stable-image-ultra': {
                endpoint: '/stable-image/generate/ultra',
                name: 'Stable Image Ultra',
                description: 'Highest quality image generation'
            },
            'stable-image-core': {
                endpoint: '/stable-image/generate/core',
                name: 'Stable Image Core',
                description: 'Fast, high-quality generation'
            },
            'sd3-large': {
                endpoint: '/stable-image/generate/sd3',
                name: 'SD3.5 Large',
                description: 'Stable Diffusion 3.5 Large'
            }
        };
    }

    // Set API key
    setApiKey(apiKey) {
        this.apiKey = apiKey;
        console.log('Stability AI API key configured');
    }

    // Set model
    setModel(modelId) {
        if (this.models[modelId]) {
            this.currentModel = modelId;
            console.log(`Switched to model: ${this.models[modelId].name}`);
        } else {
            console.warn(`Unknown model: ${modelId}, using default: ${this.defaultModel}`);
        }
    }

    // Main thumbnail generation method
    async generateThumbnails(prompts, options = {}) {
        try {
            console.log('Starting thumbnail generation with Stability AI...');

            // For single thumbnail generation, use the first prompt only
            const prompt = Array.isArray(prompts) ? prompts[0] : prompts;
            console.log('Generating single thumbnail...');

            const result = await this.generateSingleThumbnail(prompt, options);

            return {
                success: result.success,
                thumbnails: result.success ? [result.imageUrl] : [],
                failed: result.success ? [] : [result],
                totalGenerated: result.success ? 1 : 0
            };

        } catch (error) {
            console.error('Error generating thumbnail:', error);
            throw error;
        }
    }

    // Generate single thumbnail with enhanced options
    async generateSingleThumbnail(prompt, options = {}) {
        if (!this.apiKey) {
            throw new Error('Stability AI API key not configured');
        }

        try {
            console.log(`Generating thumbnail with ${this.models[this.currentModel].name}...`);
            console.log('Prompt:', prompt);

            // Enhance prompt for thumbnail generation
            const enhancedPrompt = this.enhancePromptForThumbnail(prompt, options);

            // Create generation request
            const imageUrl = await this.createGeneration(enhancedPrompt, options);

            console.log('Thumbnail generated successfully');
            return {
                success: true,
                imageUrl: imageUrl,
                prompt: enhancedPrompt,
                model: this.currentModel
            };

        } catch (error) {
            console.error('Single thumbnail generation failed:', error);

            // Try fallback generation
            try {
                console.log('Attempting fallback thumbnail generation...');
                const fallbackUrl = await this.generateFallbackThumbnail(prompt);
                return {
                    success: true,
                    imageUrl: fallbackUrl,
                    prompt: prompt,
                    model: 'fallback',
                    isFallback: true
                };
            } catch (fallbackError) {
                console.error('Fallback generation also failed:', fallbackError);
                return {
                    success: false,
                    error: error.message,
                    prompt: prompt
                };
            }
        }
    }

    // Enhanced prompt for thumbnail generation
    enhancePromptForThumbnail(basePrompt, options = {}) {
        let enhancedPrompt = basePrompt;

        // Add thumbnail-specific enhancements
        const thumbnailEnhancements = [
            'YouTube thumbnail style',
            '16:9 aspect ratio',
            'high quality',
            'detailed',
            'eye-catching',
            'professional'
        ];

        // Add style profile enhancements
        if (options.styleProfile) {
            const styleEnhancements = {
                'professional': 'clean, modern design, corporate style',
                'vibrant': 'vibrant colors, high contrast, energetic',
                'minimalist': 'minimalist, clean, simple composition',
                'dramatic': 'dramatic lighting, bold composition, cinematic',
                'playful': 'playful, colorful, fun, engaging',
                'elegant': 'elegant, sophisticated, refined aesthetic'
            };

            const enhancement = styleEnhancements[options.styleProfile.toLowerCase()];
            if (enhancement) {
                thumbnailEnhancements.push(enhancement);
            }
        }

        enhancedPrompt += ', ' + thumbnailEnhancements.join(', ');

        console.log('Enhanced prompt:', enhancedPrompt);
        return enhancedPrompt;
    }

    // Create generation request to Stability AI
    async createGeneration(prompt) {
        const modelConfig = this.models[this.currentModel];
        const endpoint = `${this.baseUrl}${modelConfig.endpoint}`;

        // Prepare form data
        const formData = new FormData();
        formData.append('prompt', prompt);
        formData.append('aspect_ratio', '16:9'); // Perfect for thumbnails
        formData.append('output_format', 'jpeg');

        // Add model-specific parameters
        if (this.currentModel === 'stable-image-ultra') {
            // Ultra-specific parameters
            formData.append('model', 'stable-image-ultra');
        } else if (this.currentModel === 'stable-image-core') {
            // Core-specific parameters
            formData.append('model', 'stable-image-core');
        } else if (this.currentModel.startsWith('sd3')) {
            // SD3-specific parameters
            formData.append('model', this.currentModel);
            formData.append('mode', 'text-to-image');
        }

        console.log(`Making request to: ${endpoint}`);

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Accept': 'image/*'
            },
            body: formData
        });

        if (!response.ok) {
            let errorMessage = `Stability AI API error: ${response.status} ${response.statusText}`;
            try {
                const errorData = await response.json();
                errorMessage = `Stability AI API error: ${errorData.message || errorData.error || errorMessage}`;
            } catch (e) {
                // If we can't parse JSON, use the default error message
            }
            throw new Error(errorMessage);
        }

        // Convert response to blob and create object URL
        const imageBlob = await response.blob();
        const imageUrl = URL.createObjectURL(imageBlob);

        return imageUrl;
    }

    // Generate with progress tracking
    async generateWithProgress(prompts, progressCallback) {
        try {
            if (progressCallback) {
                progressCallback({ percentage: 0, status: 'Initializing Stability AI generation...' });
            }

            const prompt = Array.isArray(prompts) ? prompts[0] : prompts;

            if (progressCallback) {
                progressCallback({ percentage: 25, status: 'Sending request to Stability AI...' });
            }

            if (progressCallback) {
                progressCallback({ percentage: 50, status: 'Processing image generation...' });
            }

            const result = await this.generateSingleThumbnail(prompt);

            if (progressCallback) {
                progressCallback({ percentage: 100, status: 'Generation complete!' });
            }

            return {
                success: result.success,
                thumbnails: result.success ? [result.imageUrl] : [],
                failed: result.success ? [] : [result],
                totalGenerated: result.success ? 1 : 0
            };

        } catch (error) {
            if (progressCallback) {
                progressCallback({ percentage: 0, status: 'Generation failed' });
            }
            throw error;
        }
    }

    // Test API connection
    async testConnection() {
        if (!this.apiKey) {
            console.error('Stability AI API key not configured');
            return false;
        }

        try {
            console.log('Testing Stability AI API connection...');

            // Test with a simple generation request
            const testPrompt = "simple test image";
            const formData = new FormData();
            formData.append('prompt', testPrompt);
            formData.append('model', this.currentModel);
            formData.append('aspect_ratio', '1:1');
            formData.append('output_format', 'jpeg');

            const modelConfig = this.models[this.currentModel];
            const endpoint = `${this.baseUrl}${modelConfig.endpoint}`;

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Accept': 'image/*'
                },
                body: formData
            });

            if (response.ok) {
                console.log('Stability AI API connection successful');
                return true;
            } else {
                console.error('Stability AI API connection failed:', response.status, response.statusText);
                return false;
            }

        } catch (error) {
            console.error('Stability AI API connection failed:', error.message);
            return false;
        }
    }

    // Generate fallback thumbnail when API fails
    async generateFallbackThumbnail(prompt) {
        console.log('Generating fallback SVG thumbnail...');

        // Extract key words from prompt for thumbnail
        const words = prompt.split(' ').filter(word =>
            word.length > 3 &&
            !['professional', 'youtube', 'thumbnail', 'vibrant', 'colors', 'bold', 'typography'].includes(word.toLowerCase())
        ).slice(0, 3);

        const title = words.join(' ').toUpperCase() || 'THUMBNAIL';

        // Create a simple SVG thumbnail
        const svg = `
            <svg width="1024" height="576" xmlns="http://www.w3.org/2000/svg">
                <defs>
                    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                        <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                    </linearGradient>
                </defs>
                <rect width="100%" height="100%" fill="url(#grad)"/>
                <text x="512" y="288" font-family="Arial, sans-serif" font-size="48" font-weight="bold"
                      text-anchor="middle" fill="white" stroke="black" stroke-width="2">${title}</text>
                <text x="512" y="350" font-family="Arial, sans-serif" font-size="24"
                      text-anchor="middle" fill="white" opacity="0.8">Generated by Stability AI</text>
            </svg>
        `;

        // Convert SVG to blob URL
        const blob = new Blob([svg], { type: 'image/svg+xml' });
        return URL.createObjectURL(blob);
    }

    // Add prompt variation for multiple generations
    addPromptVariation(basePrompt, variationIndex) {
        const variations = [
            '', // Original
            ' with dynamic lighting',
            ' with bold typography',
            ' with vibrant background',
            ' with dramatic composition',
            ' with professional styling'
        ];

        return basePrompt + (variations[variationIndex] || '');
    }

    // Get available models
    getAvailableModels() {
        return Object.keys(this.models).map(id => ({
            id: id,
            name: this.models[id].name,
            description: this.models[id].description
        }));
    }
}

// Make available globally
window.StabilityProService = StabilityProService;
