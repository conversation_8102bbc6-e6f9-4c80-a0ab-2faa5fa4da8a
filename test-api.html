<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log-container {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>🔧 Thumbnail Generator API Test</h1>

    <div class="test-container">
        <h2>Connection Tests</h2>
        <button onclick="testGemini()">Test Gemini 1.5 Pro</button>
        <button onclick="testReplicate()">Test Replicate FLUX.1</button>
        <button onclick="testBothServices()">Test Both Services</button>
        <button onclick="clearLog()">Clear Log</button>

        <div id="test-results"></div>
    </div>

    <div class="test-container">
        <h2>Generation Test</h2>
        <input type="text" id="test-prompt" placeholder="Enter test video title..." style="width: 70%; padding: 8px;">
        <button onclick="testGeneration()">Test Generation</button>

        <div id="generation-results"></div>
    </div>

    <div class="test-container">
        <h2>Debug Log</h2>
        <div id="log-container" class="log-container"></div>
    </div>

    <!-- Include the services -->
    <script src="gemini-pro-service.js"></script>
    <script src="stability-pro-service.js"></script>

    <script>
        // Initialize services
        const geminiPro = new GeminiProService();
        const replicatePro = new ReplicateProService();

        // Override console.log to capture logs
        const originalLog = console.log;
        const originalError = console.error;
        const logContainer = document.getElementById('log-container');

        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> ${message}`;
            if (type === 'error') {
                logEntry.style.color = '#dc3545';
            } else if (type === 'success') {
                logEntry.style.color = '#28a745';
            }
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'info');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };

        function showResult(containerId, message, isSuccess) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        async function testGemini() {
            console.log('🧪 Testing Gemini 1.5 Pro connection...');
            try {
                const result = await geminiPro.testConnection();
                const message = result ? '✅ Gemini 1.5 Pro: Connected' : '❌ Gemini 1.5 Pro: Failed';
                showResult('test-results', message, result);
            } catch (error) {
                showResult('test-results', `❌ Gemini 1.5 Pro: Error - ${error.message}`, false);
            }
        }

        async function testReplicate() {
            console.log('🧪 Testing Replicate FLUX.1 connection...');
            try {
                const result = await replicatePro.testConnection();
                const message = result ? '✅ Replicate FLUX.1: Connected' : '❌ Replicate FLUX.1: Failed';
                showResult('test-results', message, result);
            } catch (error) {
                showResult('test-results', `❌ Replicate FLUX.1: Error - ${error.message}`, false);
            }
        }

        async function testBothServices() {
            console.log('🧪 Testing both services...');
            document.getElementById('test-results').innerHTML = '';

            await testGemini();
            await testReplicate();

            console.log('✅ All tests completed');
        }

        async function testGeneration() {
            const prompt = document.getElementById('test-prompt').value.trim();
            if (!prompt) {
                alert('Please enter a test video title');
                return;
            }

            console.log(`🎨 Testing single thumbnail generation for: "${prompt}"`);
            document.getElementById('generation-results').innerHTML = '';

            try {
                // Test prompt generation
                const testPrompt = `Professional YouTube thumbnail for "${prompt}", vibrant colors, bold typography, high contrast`;
                console.log('Generated test prompt:', testPrompt);

                // Test single thumbnail generation with progress
                const result = await replicatePro.generateWithProgress([testPrompt], (progress) => {
                    console.log(`Progress: ${progress.percentage}% - ${progress.status}`);
                    showResult('generation-results', `🔄 ${progress.status} (${progress.percentage}%)`, true);
                });

                if (result.success && result.thumbnails.length > 0) {
                    showResult('generation-results', `✅ Single thumbnail generated successfully!`, true);

                    // Show the generated image
                    const img = document.createElement('img');
                    img.src = result.thumbnails[0];
                    img.style.maxWidth = '300px';
                    img.style.marginTop = '10px';
                    img.style.borderRadius = '5px';
                    img.alt = 'Generated Thumbnail';
                    document.getElementById('generation-results').appendChild(img);

                    // Add download link
                    const downloadLink = document.createElement('a');
                    downloadLink.href = result.thumbnails[0];
                    downloadLink.download = `${prompt.replace(/[^a-zA-Z0-9]/g, '_')}_thumbnail.png`;
                    downloadLink.textContent = '📥 Download Thumbnail';
                    downloadLink.style.display = 'block';
                    downloadLink.style.marginTop = '10px';
                    downloadLink.style.textAlign = 'center';
                    document.getElementById('generation-results').appendChild(downloadLink);
                } else {
                    showResult('generation-results', `❌ Generation failed: ${result.failed[0]?.error || 'Unknown error'}`, false);
                }

            } catch (error) {
                showResult('generation-results', `❌ Generation error: ${error.message}`, false);
            }
        }

        function clearLog() {
            logContainer.innerHTML = '';
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🚀 API Test Page Loaded');
            console.log('Click buttons above to test individual services');
        });
    </script>
</body>
</html>
