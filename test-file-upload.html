<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            background: #fafbfc;
            transition: all 0.3s ease;
        }
        .upload-area:hover {
            border-color: #3182ce;
            background: #f7fafc;
        }
        .upload-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        .upload-text {
            font-size: 16px;
            color: #4a5568;
            margin-bottom: 16px;
        }
        .file-preview {
            margin-top: 20px;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        .preview-item {
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .preview-item img {
            width: 100%;
            height: 100px;
            object-fit: cover;
        }
        .remove-btn {
            position: absolute;
            top: 4px;
            right: 4px;
            background: rgba(255,0,0,0.8);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 14px;
            line-height: 1;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background: #f1f5f9;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        button {
            background: #3182ce;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:disabled {
            background: #a0aec0;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>File Upload Test</h1>
    <p>This is a simple test to verify file upload functionality works correctly.</p>

    <div class="upload-area" id="upload-area">
        <div class="upload-icon">📁</div>
        <div class="upload-text">
            Drop your files here<br>
            or click to browse
        </div>
        <input type="file" id="file-input" multiple accept="image/*" style="display: none;">
    </div>

    <button id="upload-btn" disabled>Upload Files</button>
    <button onclick="clearLog()">Clear Log</button>

    <div class="file-preview" id="file-preview" style="display: none;">
        <h3>Selected Files:</h3>
        <div class="preview-grid" id="preview-grid"></div>
    </div>

    <div class="log" id="log"></div>

    <script>
        class FileUploadTest {
            constructor() {
                this.uploadedFiles = [];
                this.initializeElements();
                this.bindEvents();
                this.log('File upload test initialized');
            }

            initializeElements() {
                this.uploadArea = document.getElementById('upload-area');
                this.fileInput = document.getElementById('file-input');
                this.uploadBtn = document.getElementById('upload-btn');
                this.filePreview = document.getElementById('file-preview');
                this.previewGrid = document.getElementById('preview-grid');

                // Debug: Check if elements are found
                this.log('Upload area found: ' + !!this.uploadArea);
                this.log('File input found: ' + !!this.fileInput);
                this.log('Upload button found: ' + !!this.uploadBtn);
            }

            bindEvents() {
                // Upload events with debugging
                this.uploadArea.addEventListener('click', () => {
                    this.log('Upload area clicked, triggering file input...');
                    this.fileInput.click();
                });

                this.uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
                this.uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
                this.uploadArea.addEventListener('drop', this.handleDrop.bind(this));
                this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
                this.uploadBtn.addEventListener('click', this.handleUpload.bind(this));

                this.log('Event listeners bound successfully');
            }

            handleDragOver(e) {
                e.preventDefault();
                this.uploadArea.style.borderColor = '#3182ce';
                this.uploadArea.style.background = '#f7fafc';
                this.log('Drag over detected');
            }

            handleDragLeave(e) {
                e.preventDefault();
                this.uploadArea.style.borderColor = '#cbd5e0';
                this.uploadArea.style.background = '#fafbfc';
                this.log('Drag leave detected');
            }

            handleDrop(e) {
                e.preventDefault();
                this.uploadArea.style.borderColor = '#cbd5e0';
                this.uploadArea.style.background = '#fafbfc';

                const files = Array.from(e.dataTransfer.files);
                this.log('Files dropped: ' + files.length);
                this.processFiles(files);
            }

            handleFileSelect(e) {
                this.log('File input changed, files selected: ' + e.target.files.length);
                const files = Array.from(e.target.files);
                this.processFiles(files);
            }

            processFiles(files) {
                this.log('Processing ' + files.length + ' files...');
                
                // Filter image files
                const imageFiles = files.filter(file => file.type.startsWith('image/'));
                this.log('Image files found: ' + imageFiles.length);

                if (imageFiles.length === 0) {
                    alert('Please select image files only.');
                    return;
                }

                if (imageFiles.length > 10) {
                    alert('Maximum 10 files allowed.');
                    return;
                }

                this.uploadedFiles = imageFiles;
                this.displayFilePreview();
                this.uploadBtn.disabled = false;
                this.log('Files processed successfully');
            }

            displayFilePreview() {
                this.previewGrid.innerHTML = '';

                this.uploadedFiles.forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'preview-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" alt="Preview ${index + 1}">
                            <button class="remove-btn" onclick="testApp.removeFile(${index})">×</button>
                        `;
                        this.previewGrid.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                });

                this.filePreview.style.display = 'block';
                this.log('File preview displayed');
            }

            removeFile(index) {
                this.log('Removing file at index: ' + index);
                this.uploadedFiles.splice(index, 1);
                this.displayFilePreview();

                if (this.uploadedFiles.length === 0) {
                    this.filePreview.style.display = 'none';
                    this.uploadBtn.disabled = true;
                }
            }

            handleUpload() {
                this.log('Upload button clicked with ' + this.uploadedFiles.length + ' files');
                alert('Upload functionality works! ' + this.uploadedFiles.length + ' files ready to upload.');
            }

            log(message) {
                const logElement = document.getElementById('log');
                const timestamp = new Date().toLocaleTimeString();
                logElement.innerHTML += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
                console.log(message);
            }
        }

        // Initialize the test
        const testApp = new FileUploadTest();

        // Global function for clearing log
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Make testApp available globally for debugging
        window.testApp = testApp;
    </script>
</body>
</html>
